import {Button, ButtonGroup, Grid} from "@eccosolutions/ecco-mui";
import * as React from "react";
import {FC, useState} from "react";
import ReferralsListControl from "../ReferralsListControl";
import {CommandForm, handleLazy, LoadSRWithEntitiesContext, TasksControlNG, useAppBarOptions} from "ecco-components";
import {useControl} from "../../components/ControlWrapper";
import {Route} from "react-router";
import {EmergencyDetailsForm} from "./EmergencyDetailsForm";

interface Props {
    buildingId?: number
}

/**
 * my/team referrals list
 * buildingId is NOT passed in
 */
export const MyReferralsList: FC<Props> = ({buildingId}) => {

    const [onlyMine, setOnlyMine] = useState(true)
    const [showTask, setShowTask] = useState(true)

    // @ts-ignore because ... pain!
    const Control = useControl(ReferralsListControl, [onlyMine, buildingId])

    const Actions = () =>
        <Grid container alignItems="baseline">
            <Grid item // xs implies xs={true} which gives us flex-grow: 1 to take up space
            >live referrals</Grid>
            <Grid item>
                <ButtonGroup size="small" aria-label="toggle team">
                    <Button onClick={() => setOnlyMine(true)}
                            style={onlyMine ? {textDecoration: "underline"} : undefined}
                    >
                        my
                    </Button>
                    <Button onClick={() => setOnlyMine(false)}
                            style={!onlyMine ? {textDecoration: "underline"} : undefined}
                    >
                        team
                    </Button>
                </ButtonGroup>
            </Grid>
        </Grid>

    useAppBarOptions(<Actions/>, [onlyMine])

    // within WelcomeAppBar's own <Route /referrals to show <MyReferralsList/>
    return <>
        <Route path="/nav/r/welcome/myteam" component={() =>
            <Grid container justify="center">
                <Grid item xs={12} md={8}>
                    <Control/>
                </Grid>
            </Grid>
        }/>
        {/*<Route path="/nav/r/welcome/myteam/bob" component={() =>
            <EccoV3Modal title="bob" action="update" saveEnabled={true} show={true}
                         onCancel={() => {}} onSave={() => {}}>
                <div>BOB</div>
            </EccoV3Modal>
        }/>*/}

        {/* when on myteam page */}
        {/* could also hardcode myteam/:srId/tasks/emergencyDetails if not configured */}
        <Route exact path="/nav/r/welcome/myteam/:srId/emergencyDetails">
            {({match}) =>
                match
                    ? <LoadSRWithEntitiesContext srId={parseInt(match.params.srId)}>
                        <CommandForm onCancel={() => setShowTask(false)} onFinished={() => setShowTask(false)}>
                            {showTask && <EmergencyDetailsForm serviceRecipientId={parseInt(match.params.srId)}
                                              taskHandle={null}
                                              readOnly={true}
                            />}
                        </CommandForm>
                      </LoadSRWithEntitiesContext>
                    : null
            }
        </Route>

        {/* TODO limit to showOnQuickLog / check audit / have normal buttons also audit... */}
        <Route exact path="/nav/r/welcome/myteam/:srId/tasks/:taskName">
            {({match}) =>
                match ? <LoadSRWithEntitiesContext srId={parseInt(match.params.srId)}>
                        {/* limitToTaskNames blank so no tasks are shown, just the my/team */}
                        {handleLazy(<TasksControlNG srId={parseInt(match.params.srId)} limitToTaskNames={[]} initialActiveTaskName={match.params.taskName}/>)}
                    </LoadSRWithEntitiesContext>
                : null
            }
        </Route>
    </>
}
