import $ = require("jquery");
import * as React from "react"
import moment = require("moment");
import {EccoDateTime} from "@eccosolutions/ecco-common";

import BaseAsyncTableControl = require("../controls/BaseAsyncTableControl");
import BaseTableRowControl = require("../controls/BaseTableRowControl");
import SelectList = require("../controls/SelectList");
import commands = require("./commands");
import StringToObjectMap = types.StringToObjectMap;
import {ClientList} from "../clientdetails/components/ClientList";
import * as types from "@eccosolutions/ecco-common";
import {SparseArray} from "@eccosolutions/ecco-common";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {CommandQueue} from "ecco-commands";
import {
    apiClient,
    RiskFlagsAsTextList,
} from "ecco-components";
import {DomElementContainer, showReactInModal} from "ecco-components-core";
import {
    ReferralDto, ReportCriteriaDto,
    RiskEvidenceAjaxRepository,
    RiskFlagsSnapshotDto,
    SessionData
} from "ecco-dto";
import {ClientAttendanceDto, GroupActivityDto} from "ecco-dto/group-support-dto";
import {ReferralSummaryDto} from "ecco-dto/referral-dto";
import {getCommandQueueRepository} from "ecco-offline-data";
import {GroupSupportAjaxRepository} from "ecco-dto";
import {openReferralDirect} from "../clientdetails/components/ClientReferralsPopup";
import BaseAsyncDataControl from "../controls/BaseAsyncDataControl";
import {ServicesContextProvider} from "../offline/ServicesContextProvider";
import {attach as attachSvcCat} from "../entity-restrictions/ServiceCategorisationSelect";
import ServiceProjectSelectionControl from "../entity-restrictions/ServiceProjectSelectionControl";
import {EntityRestrictionsAjaxRepository} from "../entity-restrictions/EntityRestrictionsAjaxRepository";
import {ReportAjaxRepository} from "ecco-reports";
import {GroupActivityOptions} from "./GroupActivityList";

const repository = new GroupSupportAjaxRepository(apiClient);
const riskRepo = new RiskEvidenceAjaxRepository(apiClient)
const restrictedEntityRepository = new EntityRestrictionsAjaxRepository(apiClient);

class FlagsWithPopupControl extends BaseAsyncDataControl<RiskFlagsSnapshotDto> {
    constructor(private srId: number, private clientName: string) {
        super()
    }

    protected fetchViewData(): Promise<RiskFlagsSnapshotDto> {
        return riskRepo.findRiskFlagsSnapshotByServiceRecipientIdAndEvidenceGroup(this.srId);
    }

    protected render(items: RiskFlagsSnapshotDto) {
        const hasFlags = items.latestFlags.some(f => f.value); // show flags, if one is true
        if (hasFlags) {
            this.element()
                .append(
                    $("<i>")
                        .addClass("fa fa-flag pull-right rag-red clickable")
                        .click(() => {
                            showReactInModal(
                                "Risk flags for " + this.clientName,
                                <ServicesContextProvider>
                                    <RiskFlagsAsTextList serviceRecipientId={this.srId}/>
                                </ServicesContextProvider>,
                                {action: "close", maxWidth:"sm"})
                        })
                        // don't have name and for mobile we probably want something that's tap to show flag names
                        // possibly showReactInModal(<RiskFlagsAsTextList/>)
                        // so for now, people can just open the referral
                        // .attr("title", items.latestFlags.map(f => f.flagId.toString()).join(", "))
                )
        }
    }
}

class RowData {
    constructor(public clientAttendance: ClientAttendanceDto,
                public invited: boolean,
                public attending: boolean,
                public attended: boolean,
                public evidenceNotes: string,
                public typeId: number,
                public questionnaireQueue: CommandQueue) {}
}
export abstract class GroupActivityClientRowControl extends BaseTableRowControl<ClientAttendanceDto> {

    private $invitedCheckbox: $.JQuery;
    private $attendingCheckbox: $.JQuery;
    private $attendedCheckbox: $.JQuery;
    private $statusMsg: $.JQuery;
    private $surveyButtons: $.JQuery[] = [];
    private $evidenceNotes: $.JQuery;
    private evidenceTypeList = new SelectList("comment-type-selection");
    private questionnaireGroupQueues: SparseArray<CommandQueue> = {}; // questionnaireGroupId to command queue
    private hasChange: boolean = false;

    constructor(private sessionData: SessionData, private activity: GroupActivityDto,
                private options: GroupActivityOptions,
                private clientAttendance: ClientAttendanceDto, private printable: boolean,
                private onChangeCallback: () => void) {
        super(clientAttendance);
    }

    protected override getColumnMapping(): StringToObjectMap<(dto: ClientAttendanceDto) => string|$.JQuery> {
        return {
            "client": (item: ClientAttendanceDto) => this.buildClientName(item),
            "invited": (item: ClientAttendanceDto) => this.buildInvitedCheckbox(item),
            "attending": (item: ClientAttendanceDto) => this.buildAttendingCheckbox(item),
            "attended": (item: ClientAttendanceDto) => this.buildAttendedCheckbox(item),
            //"surveys": (item: ClientAttendanceDto) => this.buildSurveys(item),
            "support note": (item: ClientAttendanceDto) => this.buildEvidenceNotes(item),

            "register": (item: ClientAttendanceDto) => this.buildInvitedCheckbox(item),
            "scheduled": (item: ClientAttendanceDto) => this.buildAttendingCheckbox(item),
            "sent": (item: ClientAttendanceDto) => this.buildAttendedCheckbox(item),
            "status": (item: ClientAttendanceDto) => this.buildStatus(item)
        };
    }

    public getRowData(): RowData {
        if (!this.hasChange) {
            return null;
        }

        let invited = this.$invitedCheckbox.is(":checked");
        let attending = invited && this.$attendingCheckbox && this.$attendingCheckbox.is(":checked");
        let attended = attending && this.$attendedCheckbox && this.$attendedCheckbox.is(":checked");
        let evidenceNotes = this.$evidenceNotes && this.$evidenceNotes.val();
        let typeId = this.evidenceTypeList.selected(true);
        let flattenedSurveys = this.flattenCmdSparseArray(this.questionnaireGroupQueues);
        return new RowData(this.clientAttendance, invited, attending, attended, evidenceNotes, typeId, flattenedSurveys);
    }

    public setInvited(newValue: boolean) {
        // if the loaded data was invited, then we can't change it
        // if we are a session - but we can un-invite courses
        if (this.clientAttendance && this.clientAttendance.invited && !this.activity.course) {
            return;
        }
        const currAttending = !this.activity ? this.$attendingCheckbox.is(":checked") : newValue;
        if (newValue) { // if changing to on - always turn on
            this.$invitedCheckbox.prop("checked", true);
        } else { // if changing to off, only if not attending
            this.$invitedCheckbox.prop("checked",  currAttending);
        }
        this.onChange();
    }
    public setAttending(newValue: boolean) {
        if (this.clientAttendance && this.clientAttendance.attending) {
            return;
        }
        const currInvited = this.$invitedCheckbox.is(":checked");
        if (newValue) { // if changing to on - only if invited
            this.$attendingCheckbox.prop("checked", currInvited);
        } else { // if changing to off - always turn off
            this.$attendingCheckbox.prop("checked", false);
        }
        this.onChange();
    }

    private buildClientName(attendance: ClientAttendanceDto): $.JQuery {
        const flags = new FlagsWithPopupControl(attendance.serviceRecipientId, attendance.referralSummary.clientDisplayName)
        flags.load()
        const $container = $('<div>');
        const $name = $('<div>')
            .append($("<span>").append(attendance.referralSummary.displayName))
            .append($("<span>").addClass("pull-right").append(flags.element()));
        $container.append($name);

        // z4467
        const readOnly = attendance.referralSummary._readOnly
        const spanOrLink = readOnly ? $("<span>") : $("<a>");
        const $referralFile = spanOrLink.text(attendance.referralSummary.referralCode || attendance.referralSummary.referralId.toString());
        if (!readOnly) {
            $referralFile.click(() => {
                openReferralDirect(attendance.referralSummary.referralId, true);
            });
        }
        const $rId = $("<span>").text("r-id: ").append($referralFile);
        const $cId = $("<span>").text(" c-id: " + (attendance.referralSummary.clientCode || attendance.referralSummary.clientId.toString()));
        $container.append(
                $('<div class="small text-muted text-nowrap">')
                        .append($rId).append($cId)
        );
        if (attendance.referralSummary.receivingServiceDate) {
            $container.append(
                $('<div class="small text-muted text-nowrap">')
                    .append("from " + moment(attendance.referralSummary.receivingServiceDate).format("DD MMM YYYY")));
        }
        if (attendance.referralSummary.exitedDate) {
            $container.append(
                $('<div class="small text-muted text-nowrap">')
                    .append("to " + moment(attendance.referralSummary.exitedDate).format("DD MMM YYYY")));
        }
        return $container;
    }

    /*private buildSurveys(attendance: ClientAttendanceDto): $.JQuery {
        if (this.printable) return null;

        if (!this.activity.activityType.linkedQuestionGroups
                    || this.activity.activityType.linkedQuestionGroups.length == 0) {
            return null;
        }

        this.activity.activityType.linkedQuestionGroups.forEach(linkedQg => {
            const questionGroup = this.sessionData.getQuestionGroupById(linkedQg.questionGroupId);
            const button = new ActionButton(questionGroup.name)
                .addClass("btn")
                .autoDisable(false)
                .disable()
                .clickSynchronous( () => {
                    QuestionnaireWizardForm.showInModalByIds(
                        this.sessionData,
                        attendance.serviceRecipientId,
                        EvidenceGroup.fromName(linkedQg.evidenceGroupKey),
                        "groupSupport", //see EditInvitationCommandViewModel.GROPUSUPPORT_TASKID
                        "",
                        questionGroup.questions,
                        (cmdQueue: CommandQueue) => {
                            this.questionnaireGroupQueues[linkedQg.questionGroupId] = cmdQueue;
                            this.onChange();
                        },
                        null,
                        () => {});
                });
            this.$surveyButtons.push(button.element());
        });

        return this.$surveyButtons.reduce(($prev, $curr, idx, []) => $prev.append($curr), $("<span>"));
    }*/

    private buildEvidenceNotes(attendance: ClientAttendanceDto): $.JQuery {
        if (this.printable) return null;

        let value = attendance.comment;

        this.$evidenceNotes = $("<textarea>")
            .addClass("form-control")
            .append(attendance.comment)
            .prop("disabled", true) // only enable dynamically on attended transition. Was !attendance.attending || !_.isEmpty(value))
            .keyup( () => {
                if (this.$evidenceNotes.val() != value) {
                    value = this.$evidenceNotes.val();
                    this.onChange();
                }
            });

        const serviceType = this.sessionData.getServiceTypeByServiceCategorisationId(attendance.referralSummary.serviceAllocationId);
        const taskName = serviceType.getFirstSupportTaskName(this.sessionData)
        const commentTypes = serviceType.getCommentTypesById(this.sessionData, taskName);

        this.evidenceTypeList.element().hide();

        this.evidenceTypeList.populateFromList(commentTypes,
            (ct) => ({ key: ct.getId().toString(), value: ct.getDisplayName(), isHidden: ct.getDisabled(), isDefault: false, readOnly: false }),
            (ct) => attendance.typeId == ct.getId());
        this.evidenceTypeList.change( () => {
            this.onChange();
        });
        this.evidenceTypeList.element().prop("disabled", true);

        if (this.sessionData.isEnabled("groupsupport.support.commentType")) {
            this.evidenceTypeList.element().show();
        }

        return $("<div>").append(this.$evidenceNotes).append(this.evidenceTypeList.element());
    }

    private buildInvitedCheckbox(attendance: ClientAttendanceDto): $.JQuery {
        this.$invitedCheckbox = $("<input>").attr("type","checkbox");
        this.$invitedCheckbox.prop("checked", attendance.invited);

        this.$invitedCheckbox.prop("disabled", (!this.activity.course && attendance.invited) || this.printable);
        this.$invitedCheckbox.on("change", () => this.onChange());
        return this.$invitedCheckbox;
    }

    private buildAttendingCheckbox(attendance: ClientAttendanceDto): $.JQuery {
        this.$attendingCheckbox = $("<input>").attr("type","checkbox");
        this.$attendingCheckbox.prop("checked", attendance.attending);
        this.$attendingCheckbox.prop("disabled", !attendance.invited || attendance.attending || this.printable);
        this.$attendingCheckbox.on("change", () => this.onChange());
        return this.$attendingCheckbox;
    }

    private buildStatus(attendance: ClientAttendanceDto): $.JQuery {
        this.$statusMsg = $("<span>").text("-");
        return this.$statusMsg;
    }

    private buildAttendedCheckbox(attendance: ClientAttendanceDto): $.JQuery {
        if (this.printable) return null;

        this.$attendedCheckbox = $("<input>").attr("type","checkbox");
        this.$attendedCheckbox.prop("checked", attendance.attended);
        if (this.options.allowAttended) {
            this.$attendedCheckbox.prop("disabled", !attendance.attending || attendance.attended);
        } else {
            this.$attendedCheckbox.prop("disabled", true);
        }
        this.$attendedCheckbox.on("change", () => this.onChange());
        return this.$attendedCheckbox;
    }

    private onChange() {
        const invited = this.$invitedCheckbox.is(":checked");

        if (this.$attendingCheckbox && this.$attendedCheckbox) {
            const attending = invited && this.$attendingCheckbox.is(":checked");
            const attended = attending && this.$attendedCheckbox.is(":checked");
            this.$attendingCheckbox.prop("checked", attending);
            this.$attendingCheckbox.prop("disabled", !invited);
            this.$attendedCheckbox.prop("checked", attended);
            if (this.options.allowAttended) {
                this.$attendedCheckbox.prop("disabled", !attending);
            } else {
                this.$attendedCheckbox.prop("disabled", true);
            }
            this.$surveyButtons.forEach($sb => $sb.prop("disabled", !attended));
            this.$evidenceNotes && this.$evidenceNotes.prop("disabled", !attended);
            this.evidenceTypeList.element().prop("disabled", !attended);
        }

        this.onChangeCallback();
        this.hasChange = true;
    }

    private flattenCmdSparseArray(cmdQueues: SparseArray<CommandQueue>): CommandQueue {
        let result = new CommandQueue(getCommandQueueRepository());
        for (let key in cmdQueues) {
            result.addQueue(cmdQueues[key]);
        }
        return result;
    }
}

class DummyRowControl extends BaseTableRowControl<ClientAttendanceDto> {

    private $invitedCheckbox: $.JQuery;
    private $attendingCheckbox: $.JQuery;

    constructor(private sessionData: SessionData, private printable: boolean,
                private onInviteAllCallback: (newValue: boolean) => void,
                private onAttendAllCallback: (newValue: boolean) => void) {
        super(null);
    }

    protected override getColumnMapping(): StringToObjectMap<(dto: ClientAttendanceDto) => string|$.JQuery> {
        return {
            "client": (item: ClientAttendanceDto) => null,
            "invited": (item: ClientAttendanceDto) => this.buildInvitedCheckbox(),
            "attending": (item: ClientAttendanceDto) => this.buildAttendingCheckbox(),
            "attended": (item: ClientAttendanceDto) => null,
            "surveys": (item: ClientAttendanceDto) => null,
            "support note": (item: ClientAttendanceDto) => null
        };
    }

    public getRowData(): RowData {
        return null;
    }

    private buildInvitedCheckbox(): $.JQuery {
        this.$invitedCheckbox = $("<input>").attr("type","checkbox");
        this.$invitedCheckbox.prop("checked", false);
        this.$invitedCheckbox.prop("disabled", this.printable);
        this.$invitedCheckbox.click(() => this.onInviteAllCallback(this.$invitedCheckbox.prop("checked")));
        return this.$invitedCheckbox;
    }

    private buildAttendingCheckbox(): $.JQuery {
        this.$attendingCheckbox = $("<input>").attr("type","checkbox");
        this.$attendingCheckbox.prop("checked", false);
        this.$attendingCheckbox.prop("disabled", this.printable);
        this.$attendingCheckbox.click(() => this.onAttendAllCallback(this.$attendingCheckbox.prop("checked")));
        return this.$attendingCheckbox;
    }

}

export abstract class GroupActivityClientListControl extends BaseAsyncTableControl<ClientAttendanceDto> {

    private items: ClientAttendanceDto[];
    private rows = new Array<GroupActivityClientRowControl>();

    private commandQueue = new CommandQueue(getCommandQueueRepository());

    protected $saveButton: $.JQuery;

    protected printable: boolean = false;

    private serviceCategorisationModal = new ServiceCategorisationModal();

    constructor(protected sessionData: SessionData, protected activity: GroupActivityDto, protected options: GroupActivityOptions) {
        super();
    }

    protected abstract override getHeaders(): string[];
    protected abstract createRow(clientAttendance: ClientAttendanceDto);

    protected fetchViewData(): Promise<ClientAttendanceDto[]> {
        this.rows = [];
        return repository.findInvitedClientsByActivityId(this.activity.id)
            .then(caArr => {
                const sorted: ClientAttendanceDto[] = caArr.sort((a, b) =>  // return alphabetic by surname
                    a.referralSummary.lastName.localeCompare(b.referralSummary.lastName)
                );
                sorted.unshift(null); // create a blank row at the start
                return sorted;
            });
    }

    protected createRowControl(clientAttendance: ClientAttendanceDto) {
        if (clientAttendance == null) {
            return new DummyRowControl(
                this.sessionData,
                this.printable,
                (newValue: boolean) => {this.allInvites(newValue)},
                (newValue: boolean) => {this.allAttendees(newValue)});
        } else {
            let row = this.createRow(clientAttendance);
            this.rows.push(row);
            return row;
        }
    }

    private allInvites(newValue: boolean) {
        this.rows.forEach(r => r.setInvited(newValue));
    }
    private allAttendees(newValue: boolean) {
        this.rows.forEach(r => r.setAttending(newValue));
    }

    protected override render(items?: ClientAttendanceDto[]) {
        items = items && (this.items = items) || this.items;
        super.render(items.filter((item) => !this.printable || (item && item.invited)));

        const $add = $("<a>").addClass("btn btn-default").text("add client");
        this.element().append(
            $add.click(() => {
                this.findOrCreateClient(true);
            })
        );

        if (this.options.addService) {
            const $addSvcAlloc = $("<a>").addClass("btn btn-default").text("add service");
            this.element().append(
                    $addSvcAlloc.click(() => {
                        this.findServiceAllocation();
                    })
            );
        }

        if (this.printable) {
            const $printButton = this.buildPrintButton();
            this.element().append($printButton);
        } else {
            this.$saveButton = this.buildSaveButton();
            this.element().append(this.$saveButton);
        }
    }

    private buildSaveButton(): $.JQuery {
        const $button = $("<button>");
        $button.text("save");
        $button.addClass("btn btn-primary pull-right");
        $button.prop("disabled", true);
        $button.on("click", this.onSave.bind(this));
        return $button;
    }

    private buildPrintButton(): $.JQuery {
        const $button = $("<a>");
        $button.text("print");
        $button.addClass("btn btn-default pull-right");
        $button.prop("href", "javascript:window.print()");
        return $button;
    }

    // register a change
    private gatherCommands() {
        this.rows.forEach(row => {
            let rowData = row.getRowData();
            if (rowData != null) {
                const cmd = new commands.GroupActivityInvitationCommand("update",
                    this.activity.id, Uuid.parse(this.activity.uuid), rowData.clientAttendance.referralSummary.referralId);
                cmd.changeInvited(rowData.clientAttendance.invited, rowData.invited);
                cmd.changeAttending(rowData.clientAttendance.attending, rowData.attending);
                cmd.changeAttended(rowData.clientAttendance.attended, rowData.attended);
                cmd.changeEvidenceNotes(rowData.clientAttendance.comment, rowData.evidenceNotes);
                cmd.changeEvidenceType(rowData.clientAttendance.typeId, rowData.typeId);

                this.commandQueue.addCommand(cmd);

                // if we attended, add the questionnarie survey
                // see EditInvitationCommandHandler
                if (rowData.attended) {
                    this.commandQueue.addQueue(rowData.questionnaireQueue);
                }
            }
        });
    }

    private onSave() {
        this.gatherCommands();
        this.commandQueue.flushCommands().then(() => {
            super.load();
        })
        .catch( () => alert("Error submitting request. Perhaps you could report an issue to the support desk?") ) // FIXME: need to sort this out;
    }

    public setPrintable(printable: boolean) {
        this.printable = printable;
        this.render();
    }

    public isPrintable(): boolean {
        return this.printable;
    }

    private findOrCreateClient(canCreateClient: boolean) {
        ClientList.addClientReferralModal(!canCreateClient,
            referral => {
                this.addReferral(referral);
            }
        );
    }

    private findServiceAllocation() {
        this.serviceCategorisationModal.modal(this.sessionData,
                (svcCatSelected: number) => this.addClientsFromService(svcCatSelected));
    }

    private addClientsFromService(serviceCatId: number) {
        const svcCat = this.sessionData.getServiceCategorisation(serviceCatId);

        // using reports to get the data, with clients etc
        /*
        const chartDef = new ChartDefinition(chartDefinitionDto, sessionData);
        setChartDefinition(chartDef);
        ReportDataSourceFactory.getDataSource(chartDef)
            .getData()
            .then(data => {
                setChartData(data);
            });
        */

        // using referrals - but this doesn't allow live on date of activity (unless use GroupSupportActivityController)
        //referralRepository.findAllReferrals(svcCat.serviceId, svcCat.projectId, ReferralStatus.LiveAtEnd,

        // using reports directly
        ReportAjaxRepository.generateLiveReportCriteria()
            .then(criteria => {
                criteria.serviceId = svcCat.serviceId;
                criteria.projectId = svcCat.projectId;
                criteria.to = EccoDateTime.parseIso8601(this.activity.startDateTime).toEccoDate().formatIso8601();
                this.pagedReferralSummary(criteria as ReportCriteriaDto, 0, [])
                    .then(referrals => {
                        referrals.forEach(r => this.addReferral(r));
                    });
            });
    }

    private addReferral(referral: ReferralSummaryDto) {
        // first check we don't already have it
        if (this.rows.some(row => row.getRowData()?.clientAttendance.serviceRecipientId == referral.serviceRecipientId)) {
            return;
        }
        super.addRow(this.dtoFromReferral(referral));
    }

    // copied from ReportAjaxRepository
    protected pagedReferralSummary(criteria: ReportCriteriaDto, page: number, cumulativeResult:ReferralDto[]):Promise<ReferralDto[]> {
        return ReportAjaxRepository.instance.findAllReferralSummary(criteria, page)
            .then((result) => {
                if (result.length) {
                    return this.pagedReferralSummary(criteria, page + 1, cumulativeResult.concat(result));
                } else {
                    return Promise.resolve(cumulativeResult);
                }
            });
    }

    private dtoFromReferral(referral: ReferralSummaryDto): ClientAttendanceDto {
        return {
            referralSummary: referral,
            serviceRecipientId: referral.serviceRecipientId,
            invited: false,
            attending: false,
            attended: false,
            dailyAttendances: []
        };
    }
}

/*const ServiceCategorisationControl: FC<{onChange: (relationship: number | null) => void}> = props => {

    const eccoAPI = useServicesContext();

    //const [relationship, setRelationship] = useState(null);
    const onChange = (o: any) => {
        props.onChange(o && parseInt(o.value, 10));
    }

    const entries: Array<{value: string, label: string}> =
            eccoAPI.sessionData.getRelationshipAssociationTypesList().map(entry => ({value: entry.id.toString(), label: entry.name}));

    // onChange={value => areaChange(value as ListDefinitionEntry)}
    return <>
        <SelectList
                createNew={false}
                options={entries}
                onChange={onChange}
        />
        {/!* make some space, otherwise the select list options have no room to show *!/}
        <div style={{marginTop: "200px"}} />
    </>
};*/

// see ReferralsListControl
class ServiceCategorisationModal {

    private sessionData: SessionData;
    private chosenSvcCatId: number;

    public attachServiceCategorisationSelector($menu: $.JQuery) {
        const el = document.createElement('p');
        $menu.append(el);
        attachSvcCat(el, serviceCat => {
            this.chosenSvcCatId = serviceCat.id;
        });
    }

    public attachServiceProjectSelector($menu: $.JQuery) {
        restrictedEntityRepository.findRestrictedServicesProjects().then(servicesList => {
            const serviceProjectSelector = new ServiceProjectSelectionControl(servicesList,
                {
                    serviceId: null,
                    projectId: null
                },
                (selection) => {
                    this.chosenSvcCatId = this.sessionData.getServiceCategorisationByIds(selection.serviceId, selection.projectId).id;
                });
            serviceProjectSelector.element().find(".e-row").removeClass("e-row").addClass("text-center"); // Hack for now
            $menu.append($("<p>")
                    .append(serviceProjectSelector.element().addClass("inline")));
        });
    }

    public modal(sessionData: SessionData, selected: (svcCatSelected: number) => void) {
        this.chosenSvcCatId = null;
        this.sessionData = sessionData;

        const $menu = $("<div>");
        if (sessionData.isEnabled("referral.list.service-group")) {
            this.attachServiceCategorisationSelector($menu);
        } else {
            this.attachServiceProjectSelector($menu);
        }

        showReactInModal("add clients from",
            <DomElementContainer content={$menu.get(0)}/>,
            {
                action: "add",
                onAction: () => {
                    this.chosenSvcCatId && selected(this.chosenSvcCatId);
                }
            }
        );
    }

}
