import * as referralDtos from "ecco-dto";
import {BaseHistoryItemControl} from "../../evidence/BaseHistoryItemControl";
import $ = require("jquery");
import {Command} from "ecco-commands";
import {QuestionAnswerSnapshotDto} from "ecco-dto";
import {QuestionnaireWork} from "ecco-commands";
import {EvidenceDiscriminator} from "ecco-commands";
import {flagsAsGraphical} from "ecco-components";
import {reactElementContainer} from "ecco-components-core";

class QuestionnaireHistoryItemControl extends BaseHistoryItemControl<QuestionnaireWork> {
    constructor(
        serviceRecipientWithEntities: referralDtos.ServiceRecipientWithEntities,
        questionnaireWork: QuestionnaireWork,
        deleteHandler?: (cmd: Command) => void,
        private attachmentsOnly?: boolean
    ) {
        super(
            EvidenceDiscriminator.SUPPORT,
            serviceRecipientWithEntities,
            questionnaireWork,
            deleteHandler
        );
        this.render();
    }

    protected renderInternal() {
        this.element().empty();

        this.renderHeader();

        var $suffixSpace = $("<div>");
        this.renderComment($suffixSpace);

        // line 3: answers
        var $list = $("<ul>").addClass("list-unstyled");
        $list.append(
            this.work.getDto().answers.map((answer: QuestionAnswerSnapshotDto) => {
                var $entry = $("<li>").css({"margin-bottom": "5px"});
                var question = this.serviceRecipient.features.lookupQuestion(answer.questionId);
                var answerDisplayValue = this.lookupAnswerDisplayValue(
                    answer.questionId,
                    answer.answer
                );
                const q = $("<span>").css({"font-weight": "bold"}).text(question.concat(" : "));
                const a = $("<span>").css("white-space", "pre-wrap").text(answerDisplayValue);
                $entry.append(q).append(a);
                return $entry;
            })
        );
        $suffixSpace.append($list);

        this.renderAttachments(this.attachmentsOnly);

        this.renderEvidenceFlags($list, $suffixSpace);
    }

    private renderEvidenceFlags($list: $.JQuery, $suffixSpace: $.JQuery) {
        const flags = this.work.getDto().flags || [];
        let flagMount: $.JQuery | undefined = undefined;
        const el = flags.length > 0 && flagsAsGraphical(this.serviceRecipient.features, flags, false, true);

        if (el) {
            flagMount = $("<span>");
            reactElementContainer(el, flagMount[0]);
        }
        flags.length > 0 && $list.append($("<ul>").addClass("list-unstyled").append(flagMount));

        this.element().append($suffixSpace);
    }

    private lookupAnswerDisplayValue(id: number, answerValue: string): string {
        return this.serviceRecipient.features.getAnswerDisplayValue(id, answerValue);
    }
}
export = QuestionnaireHistoryItemControl;
