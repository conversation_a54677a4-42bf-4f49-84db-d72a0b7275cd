import * as React from "react"
import {EccoDate, EccoDateTime, IdNameDisabled} from "@eccosolutions/ecco-common";
import {Grid} from "@eccosolutions/ecco-mui";
import {AllocateWorkerCommand, CommandQueue, StartOnServiceCommand} from "ecco-commands";
import {
    CommandSubform,
    Loading,
    getWorkersWithAccessToFileOrSameAccessAsMe,
    useCurrentServiceRecipientWithEntities,
    withCommandForm
} from "ecco-components";
import {
    datePickerInput,
    SelectList,
    possiblyModalForm} from "ecco-components-core";
import * as services from "ecco-offline-data";
import {PrefixType} from "ecco-dto";

export function StartOnServiceDialog(props: {
    taskName: string,
    taskHandle: string;
    allocateWorkerOnly?: boolean}) {
    const {resolved: context, reload} = useCurrentServiceRecipientWithEntities()

    let dtoConverted: StartDto;
    const title = context.serviceType.lookupTaskName(props.taskName);
    switch (context.serviceRecipient.prefix) {
        case "i":
            dtoConverted = {title: title, role: "ROLE_INCIDENTS", ...context.incident};
            break;
        case "m":
            dtoConverted = {title: title, role: "ROLE_REPAIRS", ...context.repair};
            break;
        default:
            dtoConverted = {title: title, role: "ROLE_STAFF", ...context.referral};
    }

    return withCommandForm(commandForm => {
            return <StartOnService
                    dto={dtoConverted}
                    prefixType={context.serviceRecipient.prefix}
                    taskHandle={props.taskHandle}
                    commandForm={commandForm}
                    allocateWorkerOnly={props.allocateWorkerOnly}
                    afterSave={reload}
            />
        }
    );
}

interface StartDto {
    role: string;
    title?: string;
    receivedDate?: string;
    decisionDate?: string
    supportWorkerId?: number;
    receivingServiceDate?: string;
    serviceRecipientId: number;
    serviceAllocationId: number;
}
interface Props {
    dto: StartDto;
    prefixType: PrefixType;
    taskHandle: string;
    allocateWorkerOnly?: boolean;
    afterSave: () => void
}

interface State {
    supportWorker: number;
    receivingServiceDate: EccoDate;
    workersWithAccess: IdNameDisabled[];
}

// NB deprecating 'assign to accommodation' with startAccommodation
// NB deprecating 'contract start date' - unused

// also see ui-messages, eg referralView.allocateWorker=allocate key worker
const IncidentNames = {
    "allocateWorker": "allocated incident manager"
}
const DefaultNames = {
    "allocateWorker": "allocated worker"
}
type FieldNames = keyof typeof DefaultNames;

function lookupMessages(prefix: PrefixType) {
    switch (prefix) {
        case "i":
            return IncidentNames;
        default:
            return DefaultNames;
    }
}

export class StartOnService extends CommandSubform<Props, State> {

    constructor(props) {
        super(props);
        this.state = {
            supportWorker: props.dto.supportWorkerId,
            receivingServiceDate: EccoDate.parseIso8601FromDateTime(props.dto.receivingServiceDate),
            workersWithAccess: null
        };
    }

    override componentDidMount() {
        super.componentDidMount();
        services.getFeatureConfigRepository().getSessionData().then(sd => {
            return getWorkersWithAccessToFileOrSameAccessAsMe(sd, services.getWorkerRepository(), this.props.dto.serviceAllocationId, this.props.dto.role, true)
                .then(workersWithAccess => {
                    this.setState({workersWithAccess});
                });
        })
    }

    emitChangesTo(commandQueue: CommandQueue) {
        if (this.props.allocateWorkerOnly) {
            const cmd = new AllocateWorkerCommand("update", this.props.dto.serviceRecipientId, AllocateWorkerCommand.taskName, this.props.taskHandle)
                .changeAllocatedWorkerContactId(this.props.dto.supportWorkerId, this.state.supportWorker);
            if (cmd.hasChanges()) {
                commandQueue.addCommand(cmd);
            }
        } else {
            const cmd = new StartOnServiceCommand(this.props.dto.serviceRecipientId, this.props.taskHandle)
                .changeAllocatedWorkerContactId(this.props.dto.supportWorkerId, this.state.supportWorker)
                .changeReceivingServiceDate(EccoDate.parseIso8601FromDateTime(this.props.dto.receivingServiceDate), this.state.receivingServiceDate);
            if (cmd.hasChanges()) {
                commandQueue.addCommand(cmd);
            }
        }

    }

    getErrors(): string[] {
        const errors: string[] = []
        if (this.props.allocateWorkerOnly) {
            return errors;
        }

        // receivingServiceDate can be supplied blank - it defaults to today on a save
        const startNotNull = this.state.receivingServiceDate || EccoDate.todayLocalTime()
        // received date could be blank on historical data
        const receivedNotNull = this.props.dto.receivedDate ? EccoDate.parseIso8601(this.props.dto.receivedDate) : EccoDate.todayLocalTime()
        // interview date could be blank
        const interview = this.props.dto.decisionDate && EccoDateTime.parseIso8601(this.props.dto.decisionDate)

        if (startNotNull.earlierThan(receivedNotNull)) {
            errors.push("start date can't be before received");
        // 'start' sometimes is before interview date since its known commonly used as an 'assessment date'
        // we should deduce which tasks are before us in the list and be after those
        // } else if (interview && startNotNull.addDays(1).toDateTimeMidnight().earlierThan(interview)) {
        //     errors.push("start date can't be before interview");
        }
        // start date can't be before referral decided - will exist because accept on service has to happen? OR is that created in server?
        return errors;
    }

    private setter = (state: State) => this.setState(state);


    render() {
        const {props, setter, state} = this;

        const messages = (field: FieldNames) => lookupMessages(props.prefixType)[field];
        const workerOptions = state.workersWithAccess?.map(w => {
            return {
                value: w.id.toString(),
                label: w.name
            }
        }) || [];
        const workerSelectedFound = workerOptions.find(w => parseInt(w.value) === state.supportWorker);
        const workerSelectedNotFound = state.supportWorker ? {value: state.supportWorker.toString(), label: `- id ${state.supportWorker} -`} : null;
        const workerSelected  = workerSelectedFound || workerSelectedNotFound;
        const workerChange = (o: {value: string, label: string}) => {
            setter({...this.state, supportWorker: parseInt(o?.value) || null})
        };

        return possiblyModalForm(
            props.dto.title || "start",
            true, true,
            () => props.commandForm.cancelForm(),
            () => props.commandForm.submitForm().then(props.afterSave),
            false,
            false,
            <Loading loaded={state.workersWithAccess != null}>
                <Grid container>
                    <Grid item xs={12}>
                        {state.workersWithAccess && <SelectList createNew={false}
                                    options={workerOptions}
                                    value={workerSelected}
                                    placeholder={messages("allocateWorker")}
                                    onChange={workerChange}/>
                        }
                        {/*{state.workersWithAccess ? dropdownList(messages("allocateWorker"), setter, state, "supportWorker", state.workersWithAccess) : null}*/}
                    </Grid>
                    {!this.props.allocateWorkerOnly &&
                        <Grid item xs={12}>
                            {datePickerInput("receivingServiceDate", "start date", setter, state)}
                        </Grid>
                    }
                    {/*{!this.props.allocateWorkerOnly &&
                        <Grid item xs={12}>
                            {checkBox("receivingService", "has started?", setter, state)}
                        </Grid>
                    }*/}
                </Grid>
            </Loading>
        );
    }
}
