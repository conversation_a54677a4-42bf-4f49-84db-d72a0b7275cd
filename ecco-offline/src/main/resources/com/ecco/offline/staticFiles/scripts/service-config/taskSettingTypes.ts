import {apiClient} from "ecco-components";
import * as domain from "ecco-dto";
import {ListDefinitionEntry, SessionData, SessionDataAjaxRepository, TaskDefinition, TaskNames} from "ecco-dto";

import {OutcomeAjaxRepository} from "./OutcomeAjaxRepository";
import {QuestionGroupAjaxRepository} from "ecco-dto";
import $ = require("jquery");
import CheckboxGroupInput = require("../controls/CheckboxGroupInput");
import CheckboxInput = require("../controls/CheckboxInput");
import RadioGroupInput = require("../controls/RadioGroupInput");
import TextInput = require("../controls/TextInput");

const outcomeRepository = new OutcomeAjaxRepository(apiClient);
const questionGroupRepository = new QuestionGroupAjaxRepository(apiClient);
const sessionDataRepository = new SessionDataAjaxRepository(apiClient);

function arraysHaveSameContents<T>(first: T[], second: T[]) {
    return first.length == second.length
        && !first.some( element => second.indexOf(element) === -1 );
}

export type ExportType = "ONSITE" | "OFFSITE" | "CONFIG_ONLY";

export interface TaskSettingType {
    getSettingComment(): string;
    renderSettingValue(adminMode: boolean, $valueArea: $.JQuery,
        changedValue: (newValue: string) => void, taskSetting: domain.TaskSetting,
        serviceTypeId: number, taskName: string);
    isEqual(fromStr: string, toStr: string): boolean;
}

interface ValueAndDescription {
    value: string;
    description: string;
}

type OptionEntry = string | ValueAndDescription;

function unwrapOptionEntryValue( entry: OptionEntry): string {
    return typeof entry === "string" ? <string>entry : (<ValueAndDescription>entry).value;
}
function unwrapOptionEntryDescription( entry: OptionEntry): string {
    return typeof entry === "string" ? <string>entry : (<ValueAndDescription>entry).description;
}

export class TaskSettingTypeBase implements TaskSettingType {

    //static instance = new TaskSettingTypeText();
    constructor(private settingComment: string, protected options?: OptionEntry[]) {
    }

    getSettingComment() {
        return this.settingComment;
    }

    renderSettingValue(adminMode: boolean, $valueArea: $.JQuery,
        changedValue: (newValue: string) => void, taskSetting: domain.TaskSetting,
        serviceTypeId: number, taskName: string) {
        throw new Error("renderSettingValue not implemented");
    }

    isEqual(fromStr: string, toStr: string): boolean {
        return fromStr == toStr;
    }
}

export class TaskSettingTypeText extends TaskSettingTypeBase implements TaskSettingType {

    override renderSettingValue(adminMode: boolean, $valueArea: $.JQuery,
                    changedValue: (newValue: string) => void, taskSetting: domain.TaskSetting,
                    serviceTypeId: number, taskName: string) {
        if (!adminMode) {
            this.displayValueAsText($valueArea, taskSetting);
        } else {
            $valueArea.empty();
            const oldValue = taskSetting.getValue();
            const ti = new TextInput("");
            ti.setVal(oldValue);
            ti.change((val) => changedValue(val == "" ? null : val));
            $valueArea.append(ti.element().addClass("form-control"));
        }
    }

    displayValueAsText($valueArea: $.JQuery, taskSetting: domain.TaskSetting) {
        $valueArea.empty();
        const oldValue = taskSetting.getValue();
        $valueArea.text(oldValue ? oldValue : '-');
    }

}

export class TaskSettingTypeBoolean extends TaskSettingTypeText implements TaskSettingType {

    override renderSettingValue(adminMode: boolean, $valueArea: $.JQuery,
                       changedValue: (newValue: string) => void, taskSetting: domain.TaskSetting,
                       serviceTypeId: number, taskName: string) {
        $valueArea.empty();
        const oldValue = taskSetting.getValue();
        if (!adminMode) {
            // render as per text is fine
            super.renderSettingValue(adminMode, $valueArea, changedValue, taskSetting, serviceTypeId, taskName);
        } else {
            const cb = new CheckboxInput("");
            const selectedValue = unwrapOptionEntryValue(this.options[0]);
            cb.setChecked(oldValue == selectedValue);
            cb.change((val, state) => changedValue(state ? selectedValue : null));
            $valueArea.append(cb.element());
        }
    }

}

export class TaskSettingTypeSingleOption extends TaskSettingTypeText implements TaskSettingType {

    override renderSettingValue(adminMode: boolean, $valueArea: $.JQuery,
                       changedValue: (newValue: string) => void, taskSetting: domain.TaskSetting,
                       serviceTypeId: number, taskName: string) {
        $valueArea.empty();
        if (!adminMode) {
            // render as per text is fine
            super.renderSettingValue(adminMode, $valueArea, changedValue, taskSetting, serviceTypeId, taskName);
        } else {
            const rb = new RadioGroupInput(taskSetting.getName());
            rb.change((settingValue) => {
                changedValue(settingValue);
            });
            rb.populateFromList(this.options,
                (option) => {
                    let value = unwrapOptionEntryValue(option);
                    return {
                        label: unwrapOptionEntryDescription(option),
                        value: value,
                        id: taskSetting.getName().concat("_" + value), // set the id to something unique
                        selected: (taskSetting.getValue() == value),
                        readOnly: false
                    }
                });
            $valueArea.append(rb.element());
        }
    }

}

export class TaskSettingTypeMultipleOptions extends TaskSettingTypeText implements TaskSettingType {

    override renderSettingValue(adminMode: boolean, $valueArea: $.JQuery,
                       changedValue: (newValue: string) => void, taskSetting: domain.TaskSetting,
                       serviceTypeId: number, taskName: string,
                       descriptionWithId = false) {
        $valueArea.empty();
        if (!adminMode) {
            // render as per text is fine
            super.renderSettingValue(adminMode, $valueArea, changedValue, taskSetting, serviceTypeId, taskName);
        } else {
            const cb = new CheckboxGroupInput();
            cb.change((settingValue, checked) => {
                const newValueCSV = TaskSettingTypeMultipleOptions.newValueCSV(taskSetting.getValue(), settingValue, checked);
                changedValue(newValueCSV);
            });
            cb.populateFromList(this.options,
                (option) => {
                    let value = unwrapOptionEntryValue(option);
                    return {
                        label: descriptionWithId
                            ? unwrapOptionEntryDescription(option).concat(' (id ').concat(value).concat(')')
                            : unwrapOptionEntryDescription(option),
                        value: value,
                        id: taskSetting.getName().concat("_" + value), // set the id to something unique
                        selected: (taskSetting.getValue() != null)
                                    ? taskSetting.getValue().split(",").indexOf(value) >= 0 : false,
                        readOnly: false
                    }
                });
            $valueArea.append(cb.element());
        }
    }

    private static newValueCSV(oldValue: string, settingValue: string, checked: boolean): string {
        if (checked) {
            return oldValue ? oldValue.concat("," + settingValue) : settingValue;
        } else {
            const arr = oldValue.split(",");
            arr.splice(arr.indexOf(settingValue), 1);
            if (arr.length == 0) {
                return null;
            }
            return arr.join(",");
        }
    }

    override isEqual(fromStr: string, toStr: string): boolean {
        // same, could be null
        if (fromStr == toStr) {
            return true;
        }
        // one is null - can't be same
        if (fromStr == null || toStr == null) {
            return false;
        }
        // both not-null, compare
        const arr1 = fromStr.split(",");
        const arr2 = toStr.split(",");

        return arraysHaveSameContents(arr1, arr2);
    }
}

export class TaskSettingTypeMultipleOptionsFromPromise extends TaskSettingTypeMultipleOptions implements TaskSettingType {

    constructor(settingComment: string, private callback: (serviceTypeId: number, taskName: string) => Promise<OptionEntry[]>) {
        super(settingComment, []);
    }

    override renderSettingValue(adminMode: boolean, $valueArea: $.JQuery, changedValue: (newValue: string) => void,
                       taskSetting: domain.TaskSetting, serviceTypeId: number, taskName: string) {
        if (!adminMode) {
            this.displayValueAsText($valueArea, taskSetting);
        } else {
            this.callback(serviceTypeId, taskName).then((options: OptionEntry[]) => {
                this.options = options;
                super.renderSettingValue(adminMode, $valueArea, changedValue, taskSetting, serviceTypeId, taskName, true);
            });
        }
    }
}


/**
 * ===============================
 * REFERRAL ASPECT / TASK SETTINGS
 * ===============================
 * The settings we can have are listed below. We are being opinionated in our settings - only showing what we tend to configure
 * but if you search for the same 'key', eg validateComment, you will see options that might be desirable elsewhere (such as allowNullComment)
 *
 * also see allQuickGuide.tag
*/
/* TODO check against JIRA on who uses these - we aren't doing them anymore:
    hideOverviewFilter; copyReferralToInterview; restriction; setOverviewComponents
    showActionComponents=qty; showActionComponents=statusChange; showActionComponents=activity
    showMenus=emergencyDetails|spidergraph; hideOverviewFilter=y; completeAfter=1 (complete after first save);
    showActionAsRisk=y; showSupportActions=y and disableSupportActionComponents (shows support actions under the risk areas);
    commentLocation=top|bottom|none; showSaveTypes=draft|final; showWorkComponents=review
   DROPPING showMenus.access; showMenus.activity; showMenus.emergencyDetails;
*/

function _taskSettingTypesBuilder(taskName: string, taskDefinitions: TaskDefinition[]): Map<string, TaskSettingType> {

    switch (taskName) {
        case TaskNames.dataProtection:
            return taskSettingTypes_dataProtection63;
        case TaskNames.referralView:
            return taskSettingTypes_referralView18;
        case TaskNames.source:
        case TaskNames.sourceWithIndividual:
            return taskSettingTypes_source;
        case TaskNames.project:
        case TaskNames.projectRegion:
        case TaskNames.projectAsAccommodation:
            return taskSettingTypes_project;
        case TaskNames.clientWithContact:
            return taskSettingTypes_clientDetails;
        case TaskNames.clientWithContact2:
            return taskSettingTypes_clientDetails2;
        case TaskNames.referralDetails:
            // NB this works so far, but may not for future settings
            return joinDictionaries(
                    taskSettingTypes_referralDetails32,
                    taskSettingTypes_customForm_base
            );
        case TaskNames.waitingListCriteria:
            return taskSettingTypes_waitingList48;
        case TaskNames.funding:
            return taskSettingTypes_funding33;
        case TaskNames.emergencyDetails:
            return taskSettingTypes_emergencyDetails85;
        case TaskNames.assessmentDate:
            return taskSettingTypes_assessmentDate25;
        case TaskNames.assessmentDetails:
            return taskSettingTypes_assessmentDetails51;
        case TaskNames.referralAccepted:
            return taskSettingTypes_referralAccepted5;
        case TaskNames.decideFinal:
            return taskSettingTypes_decideFinal9;
        case TaskNames.start:
        case TaskNames.startAccommodation:
        case TaskNames.allocateWorker:
            return taskSettingTypes_start;
        case TaskNames.scheduleReviews:
            return taskSettingTypes_scheduleReviews17;
        case TaskNames.newMultipleReferral:
            return taskSettingTypes_newMultipleReferral78;
        case TaskNames.allocateToServices:
            return taskSettingTypes_allocateToServices104;
        case TaskNames.close:
        case TaskNames.exit:
            return taskSettingTypes_close;

            // <entry key="hr" value-ref="genericTypeHr"/>
        case TaskNames.hr:
            throw new Error("not implemented");
    }

    const taskDef = taskDefinitions.filter(td => td.name == taskName)[0];
    if (!SessionData.isTaskDefinitionDedicated(taskDef)) {

        switch (taskDef.type) {
            case "AGREEMENT":
                return joinDictionaries(
                        taskSettingTypes_customForm_base,
                        taskSettingTypes_agreements);

            case "EVIDENCE_CUSTOMFORM":
                return joinDictionaries(
                        taskSettingTypes_customForm_base,
                        taskSettingTypes_customForm);

            case "EVIDENCE_QUESTIONNAIRE":
                return joinDictionaries(
                        taskSettingTypes_evidence,
                        taskSettingTypes_evidenceQuestionnaires);

            case "EVIDENCE_RISK":
                return joinDictionaries(
                        taskSettingTypes_evidence,
                        taskSettingTypes_evidenceOutcomeBased,
                        taskSettingTypes_evidenceThreat)

            case "EVIDENCE_ROTA":
                return joinDictionaries(
                        taskSettingTypes_evidence,
                        taskSettingTypes_evidenceOutcomeBased,
                        taskSettingTypes_evidenceSupport,
                        taskSettingTypes_evidenceRota)

            case "EVIDENCE_SUPPORT":
            case "EVIDENCE_CHECKLIST":
                return joinDictionaries(
                        taskSettingTypes_evidence,
                        taskSettingTypes_evidenceOutcomeBased,
                        taskSettingTypes_evidenceSupport)

            default:
                return new Map<string, TaskSettingType>();
        }
    }

    return new Map<string, TaskSettingType>();
}

export function taskSettingTypesBuilder(taskName: string, taskDefinitions: TaskDefinition[]): Map<string, TaskSettingType> {
    return joinDictionaries(_taskSettingTypesBuilder(taskName, taskDefinitions), taskSettingTypes_all);
}

function joinDictionaries(...append: Map<string, TaskSettingType>[]): Map<string, TaskSettingType> {
    const all = new Map<string, TaskSettingType>();
    append.forEach((dict) => {
        for (const key of dict.keys()) {
            all.set(key, dict.get(key));
        }
    });
    return all;
}


// **********
// NB see also service-config-dto.ts TaskSettings which defines the keys used here
// **********

// ALL TASKS
const taskSettingTypes_all = new Map<string, TaskSettingType>();
taskSettingTypes_all.set( "smsTemplate",
    new TaskSettingTypeText("template for SMS message to send when this task is updated"));
taskSettingTypes_all.set( "letterTemplateId",
    new TaskSettingTypeText("letter template id to send when this task is updated"));
taskSettingTypes_all.set( "letterDataId",
    new TaskSettingTypeText("letter data id to send when this task is updated"));
taskSettingTypes_all.set( "accessAudit",
    new TaskSettingTypeBoolean("System will record a user access audit for this particular task (on top of client file access)", ["y"]));
taskSettingTypes_all.set( "triggerEmail",
    new TaskSettingTypeBoolean("send an email on task triggers to service configured email", ["y"]));
taskSettingTypes_all.set( "triggerEmailTo",
    new TaskSettingTypeText("send an email on task triggers 'to' this email instead IF triggerEmail is ticked"));
taskSettingTypes_all.set( "triggerEmailToCalc",
    new TaskSettingTypeText("send an email on task triggers 'to' this email calc instead"));
taskSettingTypes_all.set( "modalFullScreen",
    new TaskSettingTypeBoolean("popup the task in full screen", ["y"]));

export var taskSettingTypes_dataProtection63 = new Map<string, TaskSettingType>();
taskSettingTypes_dataProtection63.set( "titleRaw",
    new TaskSettingTypeText("title of the page when on the page"));
// taskSettingTypes_dataProtection63.setValue( "detailPages",
//     new TaskSettingTypeText("show the custom data protection detail page - e.g. to get referralDataProtection_rt use 'rt' (use 'basic' as default)"));
taskSettingTypes_dataProtection63.set( "formDefinition",
    new TaskSettingTypeText("form definition"));
taskSettingTypes_dataProtection63.set( "scope",
    new TaskSettingTypeSingleOption("scope of the data protection", ["referral", "client"]));

// consent is now part of agreements, since everything is there
// export var taskSettingTypes_consent73 = new Dictionary<string, TaskSettingType>();
export var taskSettingTypes_agreements = new Map<string, TaskSettingType>();
taskSettingTypes_agreements.set( "splitAgreement",
    new TaskSettingTypeBoolean("complete an agreement without accept/reject - for when one agreement covers many in the form definition", ["y"]));

const commentTypesOptions = new TaskSettingTypeMultipleOptionsFromPromise("the comment types to show using ids",
        (serviceTypeId, taskName) => sessionDataRepository.getSessionData().then(sd => {
            let commentTypeListName = sd.getServiceTypeById(serviceTypeId).getTaskDefinitionSetting(taskName, "commentTypeListName")
            // this is only for legacy test/uat systems where we had a 'commenttypes' global list
            if (taskName == 'referralView' && !commentTypeListName) {
                commentTypeListName = "commenttypes";
            }

            // find the new ones
            const listDefs: ListDefinitionEntry[] = [];
            if (commentTypeListName) {
                listDefs.push(...sd.getListDefinitionEntriesByListName(commentTypeListName, undefined, true));
            }

            // add the original selected entries, if any
            let commentTypesByIdCurrent = sd.getServiceTypeById(serviceTypeId).getTaskDefinitionSetting(taskName, "commentTypesById");
            if (commentTypesByIdCurrent) {
                // add to the start
                listDefs.unshift(...sd.getListDefinitionsFilteredByCsvIds(commentTypesByIdCurrent));
            }

            return commentTypeListName
                    ? listDefs.map((ld) => ({value: ld.getId().toString(), description: ld.getName()}))
                    : [];
        })
);

export var taskSettingTypes_referralView18 = new Map<string, TaskSettingType>();
taskSettingTypes_referralView18.set( "overviewPage",
        new TaskSettingTypeText("show the custom referral overview - e.g. rainbowCustomOverview for rainbowCustomOverviewDef (default is defaultViewCustomOverview)"));
taskSettingTypes_referralView18.set( "tasksToShowRestricted",
        new TaskSettingTypeText("override activiti setting"));
taskSettingTypes_referralView18.set( "tasksToShowClientView",
        new TaskSettingTypeText("override activiti setting"));
taskSettingTypes_referralView18.set( "tabOrder",
        new TaskSettingTypeText("Show tabs according to this order (comma separated technical names - default is overview,tasks," +
                "units,staff,residents,repairs,checksDue,checksHistory," +
                "support,supportFwd,extraTabs,"+
                "risk,riskFwd,supportHistory,careVisitHistory,checklistHistory,customFormHistory,riskHistory,relationships,appointments,contacts,attachments,"+
                "childServices,services,incidents,reporting,communication,audit"));
taskSettingTypes_referralView18.set( "limitedTabOrder",
        new TaskSettingTypeText("as tabOrder but for limited logins"));
taskSettingTypes_referralView18.set( "panelOrder",
        new TaskSettingTypeText("as tabOrder (comma separated technical names - default is aboutMe,support,health,risk"));
taskSettingTypes_referralView18.set( "limitedPanelOrder",
        new TaskSettingTypeText("as panelOrder but for limited logins"));
taskSettingTypes_referralView18.set( "supportTabComponents",
        new TaskSettingTypeMultipleOptions("Things to show (or not '!') when on 'support' tab", ["activityInterest", "!radarChart", "scoreChart"]));
taskSettingTypes_referralView18.set( "riskTabComponents",
        new TaskSettingTypeMultipleOptions("Things to show (or not '!') when on 'support' tab", ["!radarChart", "scoreChart"]));
taskSettingTypes_referralView18.set( "extraTabs",
        new TaskSettingTypeText("Evidence tasks to show as tabs (comma separated technical taskName's)"));
// also have showMenus, but that is when on a component
taskSettingTypes_referralView18.set( "hiddenMenus",
        new TaskSettingTypeMultipleOptions("File menus to hide", ["contacts", "calendar", "appointments", "services"]));
taskSettingTypes_referralView18.set( "hiddenTabs",
        new TaskSettingTypeMultipleOptions("Referral overview tabs to hide", ["risk", "forwardPlan", "forwardRiskPlan"]));
taskSettingTypes_referralView18.set( "servicesTab",
        new TaskSettingTypeBoolean("Show all client referrals", ["y"]));
taskSettingTypes_referralView18.set( "forwardPlanTab",
        new TaskSettingTypeMultipleOptions("Forward plan options", ["hideGoalPlan"]));
taskSettingTypes_referralView18.set( "forwardPlanRiskTab",
        new TaskSettingTypeMultipleOptions("Forward risk plan options", ["hideGoalPlan"]));
taskSettingTypes_referralView18.set( "aboutMeTabLabel",
        new TaskSettingTypeText("label to use on 'about me' tab/panel"));
taskSettingTypes_referralView18.set( "healthTabLabel",
        new TaskSettingTypeText("label to use on 'health' tab/panel"));
taskSettingTypes_referralView18.set( "supportTabLabel",
        new TaskSettingTypeText("label to use on 'support' tab/panel"));
taskSettingTypes_referralView18.set( "riskTabLabel",
        new TaskSettingTypeText("label to use on 'risk' tab/panel"));
taskSettingTypes_referralView18.set( "supportHistoryLabel",
        new TaskSettingTypeText("label to use instead of 'support history' on tabs" ));
taskSettingTypes_referralView18.set( "visitHistoryLabel",
        new TaskSettingTypeText("label to use instead of 'visit history' on tabs" ));
taskSettingTypes_referralView18.set( "riskHistoryLabel",
    new TaskSettingTypeText("label to use instead of 'risk history' on tabs" ));
taskSettingTypes_referralView18.set( "checklistHistoryLabel",
        new TaskSettingTypeText("label to use instead of 'checklist history' on tabs" ));
taskSettingTypes_referralView18.set( "formHistoryLabel",
    new TaskSettingTypeText("label to use instead of 'form history' on tabs" ));
taskSettingTypes_referralView18.set( "supportLabel",
        new TaskSettingTypeText("label to use instead of 'support' on tabs" ));
taskSettingTypes_referralView18.set( "riskLabel",
        new TaskSettingTypeText("label to use instead of 'risk' on tabs" ));
taskSettingTypes_referralView18.set( "forwardPlanLabel",
        new TaskSettingTypeText("label to use instead of 'forward plan' on tabs" ));
taskSettingTypes_referralView18.set( "forwardRiskPlanLabel",
        new TaskSettingTypeText("label to use instead of 'forward risk plan' on tabs" ));
taskSettingTypes_referralView18.set( "commentTypeListName",
        new TaskSettingTypeText("comment types list def name"));
taskSettingTypes_referralView18.set( "commentTypesById", commentTypesOptions);
taskSettingTypes_referralView18.set( "supportHoursGrp",
        new TaskSettingTypeText("questionnaireEvidenceGroup for support hours"));
taskSettingTypes_referralView18.set( "supportHoursQn",
        new TaskSettingTypeText("questionId for support hours"));

export var taskSettingTypes_project = new Map<string, TaskSettingType>();
taskSettingTypes_project.set( "titleRaw",
       new TaskSettingTypeText("title of the page when on the page"));

export var taskSettingTypes_source = new Map<string, TaskSettingType>();
taskSettingTypes_source.set( "titleRaw",
       new TaskSettingTypeText("title of the page when on the page"));

export var taskSettingTypes_clientDetails = new Map<string, TaskSettingType>();
// NB used by ClientDetailForm individualDetailsCommonFields
taskSettingTypes_clientDetails.set( "clientDetailFields",
    new TaskSettingTypeMultipleOptions("show extra client details on inbound referrals (and some client details) (beyond first/last/dob/gender/landline/mobile/email/preferred contact/address/knownAs/pronouns)", ["firstLanguageId", "ethnicOriginId", "nationalityId", "religionId", "disabilityId", "genderAtBirthId", "sexualOrientationId", "maritalStatusId", "ni", "housingBenefit", "nhs", "code"]));

export var taskSettingTypes_clientDetails2 = new Map<string, TaskSettingType>();
taskSettingTypes_clientDetails2.set( "clientDetailRequiredFields",
     new TaskSettingTypeMultipleOptions("", ["firstLanguageId", "ethnicOriginId", "nationalityId", "religionId", "disabilityId", "genderAtBirthId", "sexualOrientationId", "maritalStatusId", "ni", "housingBenefit", "nhs", "code"]));
taskSettingTypes_clientDetails2.set( "titleRaw",
     new TaskSettingTypeText("title of the page when on the page"));

export var taskSettingTypes_referralDetails32 = new Map<string, TaskSettingType>();
taskSettingTypes_referralDetails32.set( "srcGeographicArea",
    new TaskSettingTypeBoolean("use geographic area", ["y"]));

export var taskSettingTypes_waitingList48 = new Map<string, TaskSettingType>();
taskSettingTypes_waitingList48.set( "titleRaw",
    new TaskSettingTypeText("title of the page when on the page"));
taskSettingTypes_waitingList48.set( "formDefinition",
    new TaskSettingTypeText("form definition"));

export var taskSettingTypes_funding33 = new Map<string, TaskSettingType>();
taskSettingTypes_funding33.set( "formDefinition",
        new TaskSettingTypeSingleOption("which form definition to use", ["default", "ihm", "custom"]));
taskSettingTypes_funding33.set( "formDefinitionFields",
        new TaskSettingTypeMultipleOptions("which fields to use (if custom)", ["fundingDecisionDate", "fundingSourceId", "hoursOfSupport", "fundingAmount", "fundingPaymentRef", "fundingReviewDate", "fundingAccepted"]));

export var taskSettingTypes_emergencyDetails85 = new Map<string, TaskSettingType>();
taskSettingTypes_emergencyDetails85.set( "formDefinition",
    new TaskSettingTypeText("custom emergency form definition"));
taskSettingTypes_emergencyDetails85.set( "formDefinitionFields",
    new TaskSettingTypeMultipleOptions("which fields to use", ["descriptionDetails", "communicationNeeds", "emergencyKeyword", "emergencyDetails", "medicationDetails", "doctorDetails", "dentistDetails"]));

export var taskSettingTypes_customForm_base = new Map<string, TaskSettingType>();
taskSettingTypes_customForm_base.set( "titleRaw",
    new TaskSettingTypeText("title of the page when on the page"));
taskSettingTypes_customForm_base.set( "formDefinition",
    new TaskSettingTypeText("form definition"));
taskSettingTypes_customForm_base.set( "guidanceFormDefinition",
    new TaskSettingTypeText("guidance form definition"));
taskSettingTypes_customForm_base.set( "showOnQuickLog",
    new TaskSettingTypeBoolean("show on quick log", ["y"]));
taskSettingTypes_customForm_base.set( "showOnAboutMeTab",
    new TaskSettingTypeBoolean("show on about me tab", ["y"]));
taskSettingTypes_customForm_base.set( "showOnHealthTab",
    new TaskSettingTypeBoolean("show on health tab", ["y"]));
taskSettingTypes_customForm_base.set( "showOnSupportTab",
    new TaskSettingTypeBoolean("show on support tab", ["y"]));
taskSettingTypes_customForm_base.set( "showOnRiskTab",
    new TaskSettingTypeBoolean("show on risk tab", ["y"]));
taskSettingTypes_customForm_base.set( "showOnPathwayTab",
    new TaskSettingTypeBoolean("show on pathway tab", ["y"]));
taskSettingTypes_customForm_base.set( "taskNameGroup",
    new TaskSettingTypeText("the task to group data with"));

export var taskSettingTypes_customForm = new Map<string, TaskSettingType>();
taskSettingTypes_customForm.set( "captureClientSignature",
    new TaskSettingTypeBoolean("capture a signature", ["y"]));

export var taskSettingTypes_assessmentDate25 = new Map<string, TaskSettingType>();
taskSettingTypes_assessmentDate25.set( "titleRaw",
    new TaskSettingTypeText("title of the page when on the page"));
taskSettingTypes_assessmentDate25.set( "hideAssessmentDateFields",
    new TaskSettingTypeMultipleOptions("which fields to hide", ["interviewer1","interviewer2","decisionDate","firstOfferedInterviewDate","location","interviewSetupComments","interviewDna","interviewDnaComments"]));


export var taskSettingTypes_assessmentDetails51 = new Map<string, TaskSettingType>();
taskSettingTypes_assessmentDetails51.set( "detailPages",
        new TaskSettingTypeText("show the custom referral detail page - e.g. rainbow for referralDetails_rainbow1"));
taskSettingTypes_assessmentDetails51.set( "titleRaw",
        new TaskSettingTypeText("title of the page when on the page"));

export var taskSettingTypes_referralAccepted5 = new Map<string, TaskSettingType>();
taskSettingTypes_referralAccepted5.set("titleRaw",
    new TaskSettingTypeText("override the tasks display name"));
taskSettingTypes_referralAccepted5.set( "show",
    new TaskSettingTypeText("supportWorker - displays 'allocate support worker' on the 'appropriate referral'", ["supportWorker"]));
taskSettingTypes_referralAccepted5.set( "formDefinition",
    new TaskSettingTypeSingleOption("which form definition to use", ["default", "custom"]));
taskSettingTypes_referralAccepted5.set( "formDefinitionFields",
    new TaskSettingTypeMultipleOptions("which fields to use (if custom)", ["signpostedAgencyId"]));
taskSettingTypes_referralAccepted5.set( "signpostListName", new TaskSettingTypeText("signpost list def name"));

export var taskSettingTypes_decideFinal9 = new Map<string, TaskSettingType>();
taskSettingTypes_decideFinal9.set( "titleRaw",
        new TaskSettingTypeText("override the referral tasks display name"));
taskSettingTypes_decideFinal9.set( "formDefinition",
    new TaskSettingTypeSingleOption("which form definition to use", ["default", "custom"]));
taskSettingTypes_decideFinal9.set( "formDefinitionFields",
    new TaskSettingTypeMultipleOptions("which fields to use (if custom)", ["signpostedAgencyId"]));
taskSettingTypes_decideFinal9.set( "signpostListName", new TaskSettingTypeText("signpost list def name"));
taskSettingTypes_decideFinal9.set( "obeyAllowNext", new TaskSettingTypeBoolean("force this task to obey 'allow next' tickbox", ["y"]));

export var taskSettingTypes_start = new Map<string, TaskSettingType>();
taskSettingTypes_start.set( "titleRaw",
    new TaskSettingTypeText("override the tasks display name"));

export var taskSettingTypes_close = new Map<string, TaskSettingType>();
taskSettingTypes_close.set( "titleRaw",
     new TaskSettingTypeText("override the tasks display name"));
taskSettingTypes_close.set( "exitListName", new TaskSettingTypeText("exit list def name"));

export var taskSettingTypes_scheduleReviews17 = new Map<string, TaskSettingType>();
taskSettingTypes_scheduleReviews17.set( "reviewSchedule",
        new TaskSettingTypeText("review schedule - syntax: <x>d|m|w|y,...,end (e.g. 6w,3m,end)"));

export var taskSettingTypes_newMultipleReferral78 =
        new Map<string, TaskSettingType>();
taskSettingTypes_newMultipleReferral78.set( "multipleReferralService",
        new TaskSettingTypeText("family support - default serviceId for non-specified relationships (set multipleReferralService_father=5 for specifics)"));

export var taskSettingTypes_allocateToServices104 = new Map<string, TaskSettingType>();
taskSettingTypes_allocateToServices104.set( "migrateProperties",
        new TaskSettingTypeMultipleOptions("allocateToService and migrate some properties", ["project"]));

// EVIDENCE - GENERAL
export var taskSettingTypes_evidence = new Map<string, TaskSettingType>();
taskSettingTypes_evidence.set( "titleCode",
        new TaskSettingTypeText("looked-up title of the page when on the page"));
taskSettingTypes_evidence.set( "titleRaw",
        new TaskSettingTypeText("title of the page when on the page"));
taskSettingTypes_evidence.set( "sourcePageGroup",
        new TaskSettingTypeText("the history group evidence is saved with (see EvidenceDef.evidenceGroup)"));
taskSettingTypes_evidence.set( "guidanceFormDefinition",
        new TaskSettingTypeText("guidance form definition"));
// comment
taskSettingTypes_evidence.set( "tookPlaceOn",
        new TaskSettingTypeSingleOption("either of these options switch to start time (start end also triggers end time and calc's minutes spent)",
            ['date', "dateTime", "startEnd", "timer"]));
taskSettingTypes_evidence.set( "commentLabel",
        new TaskSettingTypeText("label of the comment"));
taskSettingTypes_evidence.set( "typeLabel",
        new TaskSettingTypeText("label of the type"));
taskSettingTypes_evidence.set( "commentLocation",
        new TaskSettingTypeSingleOption("allows comment form to be set explicitly to before or after smart steps",
            ["top","bottom","none"]));
taskSettingTypes_evidence.set( "commentWidth",
        new TaskSettingTypeSingleOption("wide overrides normal width of comment (& flags etc) form used on wide screens",
            ["default", "wide"]));
taskSettingTypes_evidence.set( "defaultWorkDate",
        new TaskSettingTypeSingleOption("default a work date to", ["today", "none"]));
taskSettingTypes_evidence.set( "allowOldWorkDate",
    new TaskSettingTypeBoolean("allow the work date to be older than 28 days ago", ["y"]));
taskSettingTypes_evidence.set( "allowFutureWorkDate",
    new TaskSettingTypeBoolean("allow the work date to be up to 28 days in the future (if 'ROLE_ADMINREFERRAL' permission)", ["y"]));
// menu
taskSettingTypes_evidence.set( "showMenus",
        new TaskSettingTypeMultipleOptions("show menus", ["overview"]));

// hact config
taskSettingTypes_agreements.set( "hactConfig",
    new TaskSettingTypeBoolean("change hact control to show all outstanding (eg when exiting)", ["showAllOutstanding"]));
// hact config
taskSettingTypes_evidence.set( "hactConfig",
    new TaskSettingTypeBoolean("change hact control to show all outstanding (eg when exiting)", ["showAllOutstanding"]));

// components to show as part of this form
taskSettingTypes_evidence.set( "embeddedComponentsSwitcher",
    new TaskSettingTypeBoolean("toggle the components under the evidence form", ["y"]));
taskSettingTypes_evidence.set( "embeddedComponents",
    new TaskSettingTypeMultipleOptions("other components to show below evidence form",
        ["history"]));


// history
taskSettingTypes_evidence.set( "captureClientSignature",
        new TaskSettingTypeBoolean("capture a signature", ["y"]));
taskSettingTypes_evidence.set( "captureClientSignaturePreamble",
        new TaskSettingTypeBoolean("text for the signing", ["y"]));
taskSettingTypes_evidence.set( "captureClientSignaturePreambleHtml",
        new TaskSettingTypeText("the html to show"));
// history options
taskSettingTypes_evidence.set( "showOverviewComponents",
        new TaskSettingTypeBoolean("descending tickbox", ["desc"]));

taskSettingTypes_evidence.set( "previewOnSave",
    new TaskSettingTypeSingleOption("preview screen on save (with option to go back)",
        ["none", "previewOnly", "previewAndSign"]));

taskSettingTypes_evidence.set( "showOnQuickLog",
        new TaskSettingTypeBoolean("show on quick log", ["y"]));
taskSettingTypes_evidence.set( "showOnAboutMeTab",
        new TaskSettingTypeBoolean("show on about me tab", ["y"]));
taskSettingTypes_evidence.set( "showOnHealthTab",
        new TaskSettingTypeBoolean("show on health tab", ["y"]));
taskSettingTypes_evidence.set( "showOnSupportTab",
        new TaskSettingTypeBoolean("show on support tab", ["y"]));
taskSettingTypes_evidence.set( "showOnRiskTab",
        new TaskSettingTypeBoolean("show on risk tab", ["y"]));
taskSettingTypes_evidence.set( "showOnPathwayTab",
        new TaskSettingTypeBoolean("show on pathway tab", ["y"]));
taskSettingTypes_evidence.set( "scopeHistory",
        new TaskSettingTypeText("include additional data from serviceIds (comma separated)"));



// EVIDENCE - GENERAL OUTCOME-BASED
export var taskSettingTypes_evidenceOutcomeBased = new Map<string, TaskSettingType>();
// menu
taskSettingTypes_evidenceOutcomeBased.set( "showMenus",
        new TaskSettingTypeMultipleOptions("show menus", ["overview", "attachments", "addGoal"]));
// history options
taskSettingTypes_evidenceOutcomeBased.set( "changesByOutcome",
        new TaskSettingTypeBoolean("group the outcomes on changes only", ["y"]));
taskSettingTypes_evidenceOutcomeBased.set( "overviewSupportByOutcome",
        new TaskSettingTypeBoolean("group by outcomes with overviewLinks.supportOnly", ["y"]));
taskSettingTypes_evidenceOutcomeBased.set( "overviewNeedsByOutcome",
        new TaskSettingTypeBoolean("group by outcomes with overviewLinks.needsOnly", ["y"]));
taskSettingTypes_evidenceOutcomeBased.set( "overviewLinks",
        new TaskSettingTypeMultipleOptions("show options for the history", ["needsOnly", "supportOnly", "changes"]));
// style
taskSettingTypes_evidenceOutcomeBased.set( "showEvidenceStyleAs",
        new TaskSettingTypeSingleOption("show evidence style as", ["smartStepOutcomes", "goals"]));
taskSettingTypes_evidenceOutcomeBased.set( "showVisualStyleAs",
    new TaskSettingTypeSingleOption("show evidence style as", ["tabular-new","tabular", "graphical", "checklist", "checks"]));
// outcome
taskSettingTypes_evidenceOutcomeBased.set( "showOutcomes",
        new TaskSettingTypeSingleOption("what outcomes to show (byOutcome means review-style)", ["all", "relevant", "byOutcome", "none"]));
taskSettingTypes_evidenceOutcomeBased.set( "showOutcomeIndicators",
        new TaskSettingTypeBoolean("bold outcomes with some work on them", ["hasHistory"]));
taskSettingTypes_evidenceOutcomeBased.set("outcome.guidance.guidanceAsHtml",
        new TaskSettingTypeText("guidance for the tab (legacy)"));
// actions
// NB specific config can be done per outcome - see DEV-1503 but requires manual insert, for instance
//  INSERT INTO st_referralaspectsettings (id, version, name, value, referralaspectId, servicetypeId, outcomeId) VALUES (1000, 0, 'showActionComponents', 'link,status,comment', <referralAspectId>, <serviceTypeId>, <outcomeId>);
taskSettingTypes_evidenceOutcomeBased.set( "showActionComponents",
    new TaskSettingTypeMultipleOptions("show aspects of smart steps (level 0)", ["link", "expiry", "target", "targetSchedule", "score", "status", "name", "comment"]));
taskSettingTypes_evidenceOutcomeBased.set( "showActionComponentsSubActions1",
    new TaskSettingTypeMultipleOptions("show aspects of smart steps (level 1)", ["link", "expiry", "target", "targetSchedule", "score", "status", "name", "comment"]));
taskSettingTypes_evidenceOutcomeBased.set( "showActionComponentsSubActions2",
    new TaskSettingTypeMultipleOptions("show aspects of smart steps (level 2)", ["link", "expiry", "target", "targetSchedule", "score", "status", "name", "comment"]));
taskSettingTypes_evidenceOutcomeBased.set( "showActions",
    new TaskSettingTypeSingleOption("the smart steps to show", ["all", "relevant", "unachieved"]));
taskSettingTypes_evidenceOutcomeBased.set( "showSubActions",
    new TaskSettingTypeBoolean("use sub actions", ["y"]));
taskSettingTypes_evidenceOutcomeBased.set( "showNewActions",
    new TaskSettingTypeBoolean("allow new actions", ["y"]));
taskSettingTypes_evidenceOutcomeBased.set( "validateComment",
        new TaskSettingTypeMultipleOptions("decide what validates the comments " +
            "(the 'comment box' is assumed to require validation; 'whenAchieved' refers to achieving a smart step')", ["whenAchieved", "minsSpent", "type", "clientStatusIfMeetingStatus", "clientStatus", "meetingStatus", "location"]));


// EVIDENCE - NEEDS/SUPPORT
export var taskSettingTypes_evidenceSupport = new Map<string, TaskSettingType>();
taskSettingTypes_evidenceSupport.set( "actAs",
        new TaskSettingTypeMultipleOptions(
            "the type of evidence page [note that evidence/domain.EvidenceDef overrides this setting]",
            ["assessment", "reduction"]));
        // 'review' existed in legacy but doesn't seem to do anything there - see evidence/dto.ts
taskSettingTypes_evidenceSupport.set( "useMultiEntry",
    new TaskSettingTypeBoolean("use multi entry", ["y"]));
// comment
taskSettingTypes_evidenceSupport.set( "showCommentComponents",
        new TaskSettingTypeMultipleOptions("options to show with a comment",
            ["showRiskManagementRequired", "minutesSpent", "type", "attachments", "clientStatus", "meetingStatus",
            "location", "mileage", "travelMinutes", "loneWorker", "!comment"]));
taskSettingTypes_evidenceSupport.set( "printCommentComponentsForClient",
        new TaskSettingTypeMultipleOptions("options to print with a comment",
            ["comment", "showRiskManagementRequired", "minutesSpent", "type", "attachments", "clientStatus", "meetingStatus",
                "location", "mileage", "travelMinutes"]));
taskSettingTypes_evidenceSupport.set( "printLink",
       new TaskSettingTypeBoolean("show printable link", ["y"]));

// EVIDENCE - GENERAL SNIPPET
    taskSettingTypes_evidence.set( "actionDefaultListName",
        new TaskSettingTypeText("checklist default list def name (can be overridden per in actions table)"));

    taskSettingTypes_evidence.set( "clientStatusListName",
            new TaskSettingTypeText("client status list def name"));
    taskSettingTypes_evidence.set( "meetingStatusListName",
            new TaskSettingTypeText("meeting status list def name"));
    taskSettingTypes_evidence.set( "locationListName",
        new TaskSettingTypeText("location list def name"));
    taskSettingTypes_evidence.set( "showFlags",
        new TaskSettingTypeBoolean("show flags with the comments", ["y"]));
    taskSettingTypes_evidence.set( "flagListName",
        new TaskSettingTypeText("flag list def name"));
    taskSettingTypes_evidence.set( "commentTypeListName",
        new TaskSettingTypeText("comment types list def name"));
    taskSettingTypes_evidence.set( "commentTypesById", commentTypesOptions);
// EVIDENCE - GENERAL SNIPPET

taskSettingTypes_evidenceOutcomeBased.set( "validateActionComponents",
    new TaskSettingTypeMultipleOptions("required aspects of smart steps", ["score", "targetDate"]));
taskSettingTypes_evidenceOutcomeBased.set( "validateActionComponentsSubActions1",
    new TaskSettingTypeMultipleOptions("required aspects of smart steps", ["score", "targetDate"]));
taskSettingTypes_evidenceOutcomeBased.set( "validateActionComponentsSubActions2",
    new TaskSettingTypeMultipleOptions("required aspects of smart steps", ["score", "targetDate"]));
taskSettingTypes_evidenceOutcomeBased.set( "forceActionComponents",
    new TaskSettingTypeMultipleOptions("force showing aspects of smart steps", ["score","targetDate"]));
taskSettingTypes_evidenceOutcomeBased.set( "forceActionComponentsSubActions1",
    new TaskSettingTypeMultipleOptions("force showing aspects of smart steps", ["score", "targetDate"]));
taskSettingTypes_evidenceOutcomeBased.set( "forceActionComponentsSubActions2",
    new TaskSettingTypeMultipleOptions("force showing aspects of smart steps", ["score", "targetDate"]));
taskSettingTypes_evidenceOutcomeBased.set( "clearActionComponents",
    new TaskSettingTypeMultipleOptions("clear showing aspects of smart steps", ["score"]));
taskSettingTypes_evidenceOutcomeBased.set( "clearActionComponentsSubActions1",
    new TaskSettingTypeMultipleOptions("clear showing aspects of smart steps", ["score"]));
taskSettingTypes_evidenceOutcomeBased.set( "clearActionComponentsSubActions2",
    new TaskSettingTypeMultipleOptions("clear showing aspects of smart steps", ["score"]));
taskSettingTypes_evidenceOutcomeBased.set( "scoreListName",
    new TaskSettingTypeText("score list def name, rather than 1-10"));

// EVIDENCE - GENERAL SNIPPET
    // actions
    taskSettingTypes_evidenceSupport.set( "allowActionStraightToAchieve",
            new TaskSettingTypeBoolean("allow + to go to * in the same piece of work (on review or assessmentReduction pages)", ["y"]));
    // outcomes
    taskSettingTypes_evidenceSupport.set( "outcomesById",
            new TaskSettingTypeMultipleOptionsFromPromise("the outcome to show using ids (order is obeyed here)",
                serviceTypeId => outcomeRepository.findAllOutcomeSupports().then((outcomes) =>
                    outcomes.map((outcome) => ({value: outcome.id.toString(), description: outcome.name}))
                )
            ));
// EVIDENCE - GENERAL SNIPPET

taskSettingTypes_evidenceSupport.set( "transientOutcomesByUuid",
    new TaskSettingTypeMultipleOptions("transient outcomes [only used in 'export']", []));

// OVERRIDE GENERAL
taskSettingTypes_evidenceSupport.set( "showActionComponents",
        new TaskSettingTypeMultipleOptions("show aspects of smart steps",
            ["link", "expiry", "target", "targetSchedule", "score", "status", "comment", "statusChangeReason", "activityInterest", "name"]));


// EVIDENCE - THREAT
export var taskSettingTypes_evidenceThreat = new Map<string, TaskSettingType>();
taskSettingTypes_evidenceThreat.set( "actAs",
        new TaskSettingTypeMultipleOptions("the type of evidence page", ["assessment", "reduction"]));
taskSettingTypes_evidenceThreat.set( "showCommentComponents",
        new TaskSettingTypeMultipleOptions("options to show with a comment",
            ["showRiskManagementDealsWith", "minsSpent", "type", "clientStatusIfMeetingStatus", "clientStatus", "meetingStatus", "location", "attachments"]));
taskSettingTypes_evidenceThreat.set( "evidenceGraphOptions",
        new TaskSettingTypeBoolean("shows the risk radar chart like the needs one", ["joinSpindlesOnThreat"]));
taskSettingTypes_evidenceThreat.set( "flagThreatsById",
        new TaskSettingTypeMultipleOptionsFromPromise("the flags to show using ids",
            serviceTypeId => sessionDataRepository.getSessionData().then(sd => sd.getListDefinitionEntriesByListName(domain.FLAGS_RISK)
                .map((ld) => ({value: ld.getId().toString(), description: ld.getName()}))
            )
        ));
// overwrite the same in taskSettingTypes_evidence but remove 'statusArea'
// because risk is hard-wired in StatusPanel but without the link, so setting 'statusArea' removes every link
taskSettingTypes_evidenceThreat.set( "showFlagsOn",
        new TaskSettingTypeMultipleOptions("show flags on... NB emergencySheet also obeys feature 'referral.emergency.riskFlags'",
                ["emergencySheet"]));
taskSettingTypes_evidenceThreat.set( "showOutcomeComponents",
        new TaskSettingTypeMultipleOptions("types of measurement (riskMatrix is on 'showActionComponents')",
            ["rag", "triggerControl"]));
// depreacted
taskSettingTypes_evidenceThreat.set( "disableActionComponents",
        new TaskSettingTypeMultipleOptions("doesn't show aspects of smart steps (eg 'target' for hr)",
            ["target", "status", "expiry"]));
// legacy enforced matix values where one of the pair existed, but we now relax
// this so allowBlank ticked means any value or no validation
// and not ticked means all validation (and when '+' a smart step a matrix is required)
taskSettingTypes_evidenceThreat.set( "validateRiskMatrix",
        new TaskSettingTypeBoolean("turn OFF risk matrix validation", ["allowBlank"]));
taskSettingTypes_evidenceThreat.set( "validateHazardIntervention",
    new TaskSettingTypeBoolean("turn ON risk hazard&intervention validation", ["validate"]));
taskSettingTypes_evidenceThreat.set( "showActionComponents",
        new TaskSettingTypeMultipleOptions("show aspects of smart steps",
            ["link", "expiry", "target", "status", "score", "comment", "riskMatrix", "triggerControl"]));
// outcomes
taskSettingTypes_evidenceThreat.set( "outcomesById",
    new TaskSettingTypeMultipleOptionsFromPromise("the outcome to show using ids (order is obeyed here)",
        serviceTypeId => outcomeRepository.findAllOutcomeThreats().then((outcomes) =>
            outcomes.map((outcome) => ({value: outcome.id.toString(), description: outcome.name}))
        )
    ));
taskSettingTypes_evidenceThreat.set( "scope",
        new TaskSettingTypeSingleOption("scope of the risk", ["client", "referral"]));



// EVIDENCE - QUESTIONNAIRES
export var taskSettingTypes_evidenceQuestionnaires = new Map<string, TaskSettingType>();
taskSettingTypes_evidenceQuestionnaires.set( "actAs",
        new TaskSettingTypeSingleOption("the type of evidence page", ["questions"]));
// NB contractually, we need to ensure we ask the customer has the appropriate outcome star licence (so remove this if we offer credit-card system!)
// NB 'evidenceGroupId MUST be convertible ' see commit 1035c9b and QuestionnaireEvidenceBusinessLogic
taskSettingTypes_evidenceQuestionnaires.set( "showEvidenceStyleAs",
        new TaskSettingTypeSingleOption("show evidence style [sourcePageGroup MUST be convertible to evidenceGroupName]", ["default","star","officialOutcomeStar"]));
taskSettingTypes_evidenceQuestionnaires.set( "showVisualStyleAs",
        new TaskSettingTypeSingleOption("show evidence style as", ["tabular-new","tabular"]));
taskSettingTypes_evidenceQuestionnaires.set( "showCommentComponents",
    new TaskSettingTypeMultipleOptions("options to show with a comment",
        ["minutesSpent", "type", "attachments", "!comment"]));
taskSettingTypes_evidenceQuestionnaires.set( "questions",
    new TaskSettingTypeText("the questiongroup names to show [DEPRECATED - do not use]"));
taskSettingTypes_evidenceQuestionnaires.set( "questionGroupsById",
    new TaskSettingTypeMultipleOptionsFromPromise("the question groups to show using ids (order is obeyed here)",
        serviceTypeId => questionGroupRepository.findAllQuestionGroups().then((questionGroups) =>
            questionGroups.map((qg) => ({value: qg.id.toString(), description: qg.name}))
        )
    ));
taskSettingTypes_evidenceQuestionnaires.set("questionGroup.guidance.guidanceAsHtml",
        new TaskSettingTypeText("guidance for the tab"));
taskSettingTypes_evidenceQuestionnaires.set( "questionnaireAsBlank",
        new TaskSettingTypeBoolean("start the questionnaire blank each time (nb required qns are set on db)", ["y"]));
taskSettingTypes_evidenceQuestionnaires.set( "hideCommentComponents",
        new TaskSettingTypeBoolean("hide the comment box", ["comment"]));
taskSettingTypes_evidenceQuestionnaires.set( "validateComment",
        new TaskSettingTypeSingleOption("decide if comments are required", ["whenAchieved", "allowNullComment"]));
taskSettingTypes_evidenceQuestionnaires.set( "showStarAnswersAs",
    new TaskSettingTypeSingleOption("show star answers as", ["value", "displayValue"]));
taskSettingTypes_evidenceQuestionnaires.set( "showFlags",
    new TaskSettingTypeBoolean("show flags with the comments", ["y"]));
taskSettingTypes_evidenceQuestionnaires.set( "flagListName",
    new TaskSettingTypeText("flag list def name"));
taskSettingTypes_evidenceQuestionnaires.set( "showFlagsOn",
    new TaskSettingTypeMultipleOptions("show flags on",
        ["statusArea", "emergencySheet", "visits"]));

// SUPPORT - ROTA
export const taskSettingTypes_evidenceRota = new Map<string, TaskSettingType>();
taskSettingTypes_evidenceRota.set( "showCommentComponents", taskSettingTypes_evidenceSupport.get( "showCommentComponents"));
