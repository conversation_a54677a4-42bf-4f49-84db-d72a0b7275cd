import * as dto from "../group-support-dto";
import {ActivityTypeDto, ClientAttendanceDto} from "../group-support-dto";
import {ResourceList} from "../web-api";
import {GroupActivity} from "./domain";
import {EccoDate} from "@eccosolutions/ecco-common";

export type GroupPageType = "sessions" | "courses";

export interface GroupSupportRepository {
    findActivityTypesByServiceId(serviceId: number): Promise<ActivityTypeDto[]>;

    findSupportActivitiesByParent(
        page: number,
        parentId: number
    ): Promise<ResourceList<dto.GroupActivityDto>>;

    findSupportActivities(
        page: number,
        loadType: GroupPageType,
        serviceId?: number,
        venueId?: number
    ): Promise<ResourceList<dto.GroupActivityDto>>;

    findCommsActivities(
        page: number,
        serviceId?: number
    ): Promise<ResourceList<dto.GroupActivityDto>>;

    findOneActivity(activityId: number): Promise<GroupActivity>;

    findOneActivityByUuid(activityUuid: string): Promise<GroupActivity>;

    findInvitedClientsByActivityId(activityId: number): Promise<dto.ClientAttendanceDto[]>;

    findClientAttendancesByActivityIdWithEndDate(
        activityId: number,
        fromDate: EccoDate,
        toDate: EccoDate
    ): Promise<ClientAttendanceDto[]>;
}
