import Lazy = require("lazy");
import {EccoDate} from "@eccosolutions/ecco-common";
import {ActivityTypeDto, ClientAttendanceDto, GroupActivityDto} from "../group-support-dto";
import {ApiClient} from "../web-api";
import {isNumber} from "../utils";
import {ReferralAjaxRepository} from "../referral/ReferralAjaxRepository";
import {ResourceList} from "../web-api";
import {GroupActivity} from "./domain";
import {GroupSupportRepository, GroupPageType} from "./GroupSupportRepository";
import {groupByNativeNumber} from "../utils";

export class GroupSupportAjaxRepository implements GroupSupportRepository {
    private referralRepository;
    constructor(private apiClient: ApiClient) {
        this.referralRepository = new ReferralAjaxRepository(apiClient);
    }

    public findActivityTypes(): Promise<ActivityTypeDto[]> {
        const path = `activityTypes/`;
        return this.apiClient
            .get<ActivityTypeDto[]>(path)
            .then(at => at.sort((a, b) => a.name.localeCompare(b.name)));
    }

    public findActivityTypesByServiceId(serviceId: number): Promise<ActivityTypeDto[]> {
        const path = `service/${serviceId}/activityTypes/`;

        return this.apiClient
            .get<ActivityTypeDto[]>(path)
            .then(at => at.sort((a, b) => a.name.localeCompare(b.name)));
    }

    public findNextScheduledActivitiesByServiceId(serviceId: number): Promise<GroupActivityDto[]> {
        const path = `service/${serviceId}/activities/next/`;

        return this.apiClient.get<GroupActivityDto[]>(path);
    }

    public findActivityInvolvementByReferralId(
        referralId: number,
        attendancesStartDate: EccoDate,
        attendancesEndDate: EccoDate
    ): Promise<ClientAttendanceDto[]> {
        const path = `referrals/${referralId}/activities/`;
        const query = {
            attendancesStartDate: attendancesStartDate.formatIso8601(),
            attendancesEndDate: attendancesEndDate.formatIso8601()
        };
        return this.apiClient.get<ClientAttendanceDto[]>(path, {query});
    }

    public findSupportActivitiesByParent(
        page: number,
        parentId: number
    ): Promise<ResourceList<GroupActivityDto>> {
        const path = "activities/";
        const query = {
            parentId: parentId.toString(),
            groupPageType: "sessions",
            service: undefined,
            activityType: undefined,
            venue: undefined,
            page: page.toString()
        };
        return this.apiClient.get<ResourceList<GroupActivityDto>>(path, {query});
    }

    public findSupportActivities(
        page: number,
        groupPageType: GroupPageType,
        serviceId?: number,
        activityTypeId?: number,
        venueId?: number
    ): Promise<ResourceList<GroupActivityDto>> {
        const path = "activities/";
        const query = {
            groupPageType: groupPageType,
            service: serviceId ? serviceId.toString() : undefined,
            activityType: activityTypeId ? activityTypeId.toString() : undefined,
            venue: venueId ? venueId.toString() : undefined,
            page: page.toString()
        };
        return this.apiClient.get<ResourceList<GroupActivityDto>>(path, {query});
    }

    public findCommsActivities(
        page: number,
        serviceId?: number,
        venueId?: number
    ): Promise<ResourceList<GroupActivityDto>> {
        const path = "activities-comms/";
        const query = {
            service: serviceId ? serviceId.toString() : undefined,
            page: page.toString()
        };
        return this.apiClient.get<ResourceList<GroupActivityDto>>(path, {query});
    }

    public findAuxActivities(
        page: number,
        serviceId?: number,
        venueId?: number
    ): Promise<ResourceList<GroupActivityDto>> {
        const path = "activities-aux/";
        const query = {
            service: serviceId ? serviceId.toString() : undefined,
            page: page.toString()
        };
        return this.apiClient.get<ResourceList<GroupActivityDto>>(path, {query});
    }

    public findOneActivity(activityId: number): Promise<GroupActivity> {
        return this.apiClient
            .get<GroupActivityDto>(`activities/${activityId}/`)
            .then(dto => new GroupActivity(dto));
    }

    public findOneActivityByUuid(activityUuid: string): Promise<GroupActivity> {
        return this.apiClient
            .get<GroupActivityDto>(`activities/byUuid/${activityUuid}/`)
            .then(dto => new GroupActivity(dto));
    }

    // Find those eligible for a group support activity
    public findInvitedClientsByActivityId(activityId: number): Promise<ClientAttendanceDto[]> {
        let attendancesQ = this.apiClient.get<ClientAttendanceDto[]>(
            `activities/${activityId}/clients/`
        );
        return attendancesQ.then(attendances => this.getRelatedReferralSummary(attendances));
    }

    private getRelatedReferralSummary(
        attendances: ClientAttendanceDto[]
    ): Promise<ClientAttendanceDto[]> {
        const bySrId = groupByNativeNumber(attendances, a => a.serviceRecipientId);
        const promises = Lazy(Object.keys(bySrId))
            .filter(isNumber)
            .chunk(10)
            .map((ids: string[]) => {
                return this.referralRepository
                    .findAllUnsecuredAclReferralSummaryByServiceRecipientId(ids)
                    .then(refSums => {
                        refSums.forEach(refSum => {
                            bySrId[refSum.serviceRecipientId].forEach(entity => {
                                entity.referralSummary = refSum;
                            });
                        });
                        return refSums;
                    });
            });
        return Promise.all(promises.toArray()).then(() => attendances);
    }

    public findClientAttendancesByActivityIdWithEndDate(
        activityId: number,
        fromDate: EccoDate,
        toDate: EccoDate
    ): Promise<ClientAttendanceDto[]> {
        const path = `activities/${activityId}/attendance/`;
        const query = {
            from: fromDate.formatIso8601(),
            to: toDate.formatIso8601()
        };
        const attendancesQ = this.apiClient.get<ClientAttendanceDto[]>(path, {query});
        return attendancesQ.then(attendances => this.getRelatedReferralSummary(attendances));
    }

    public findAllActivityTypes(serviceId: number): Promise<ActivityTypeDto[]> {
        return this.apiClient.get<ActivityTypeDto[]>(`service/${serviceId}/activityTypes/`);
    }
}
